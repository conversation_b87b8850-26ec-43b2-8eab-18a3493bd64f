import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestPluangPlusMemberNoSpark:
    """Unit tests for PluangPlusMember without Spark dependencies."""

    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations')
    def test_init_configuration_validation(self, mock_operations, mock_io_utils, 
                                          mock_spark_utils, mock_logger):
        """Test initialization with various configuration scenarios."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import Pluang<PERSON>lusMember
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_session = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
        
        # Test with minimal valid configuration
        minimal_config = {
            "bucket_path": "s3a://test-bucket",
            "pluang_plus_file_bucket": "test-plus-bucket",
            "offset": 0,
            "t_1": "2025-01-15",
            "t_2": "2025-01-14"
        }
        
        pluang_plus = PluangPlusMember(minimal_config)
        
        # Verify initialization
        assert pluang_plus.config == minimal_config
        assert pluang_plus.bucket_path == "s3a://test-bucket"
        assert pluang_plus.pluang_plus_file_bucket == "s3a://test-plus-bucket"
        assert pluang_plus.offset == 0
        assert pluang_plus.t_1 == "2025-01-15"
        assert pluang_plus.t_2 == "2025-01-14"
        assert pluang_plus.num_of_partition == 10
        assert pluang_plus.sleep_time_mongo == 10
        
        # Verify utility objects are created
        mock_spark_utils.assert_called_once_with("Pluang_Plus_Member_Validation")
        mock_io_utils.assert_called_once_with(mock_spark_session, minimal_config)
        mock_operations.assert_called_once_with(mock_spark_session)

    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations')
    def test_init_with_missing_config_keys(self, mock_operations, mock_io_utils, 
                                          mock_spark_utils, mock_logger):
        """Test initialization with missing configuration keys."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_session = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
        
        # Test with missing bucket_path
        incomplete_config = {
            "pluang_plus_file_bucket": "test-plus-bucket",
            "offset": 0,
            "t_1": "2025-01-15",
            "t_2": "2025-01-14"
        }
        
        pluang_plus = PluangPlusMember(incomplete_config)
        
        # Should handle missing bucket_path gracefully (returns None)
        assert pluang_plus.bucket_path is None
        assert pluang_plus.pluang_plus_file_bucket == "s3a://test-plus-bucket"

    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations')
    def test_configuration_parameter_types(self, mock_operations, mock_io_utils, 
                                          mock_spark_utils, mock_logger):
        """Test that configuration parameters are handled with correct types."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_session = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
        
        # Test with different parameter types
        config_with_types = {
            "bucket_path": "s3a://test-bucket",
            "pluang_plus_file_bucket": "test-plus-bucket",
            "offset": "0",  # String instead of int
            "t_1": "2025-01-15",
            "t_2": "2025-01-14"
        }
        
        pluang_plus = PluangPlusMember(config_with_types)
        
        # Should handle string offset
        assert pluang_plus.offset == "0"
        assert isinstance(pluang_plus.t_1, str)
        assert isinstance(pluang_plus.t_2, str)

    def test_business_logic_constants(self):
        """Test that business logic constants are set correctly."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'):
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14"
            }
            
            pluang_plus = PluangPlusMember(config)
            
            # Verify business logic constants
            assert pluang_plus.num_of_partition == 10
            assert pluang_plus.sleep_time_mongo == 10
            assert isinstance(pluang_plus.num_of_partition, int)
            assert isinstance(pluang_plus.sleep_time_mongo, int)

    def test_path_construction_logic(self):
        """Test S3 path construction logic."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'):
            
            config = {
                "bucket_path": "test-bucket",  # Without s3a:// prefix
                "pluang_plus_file_bucket": "plus-bucket",  # Without s3a:// prefix
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14"
            }
            
            pluang_plus = PluangPlusMember(config)
            
            # Verify path construction
            assert pluang_plus.bucket_path == "test-bucket"
            assert pluang_plus.pluang_plus_file_bucket == "s3a://plus-bucket"

    def test_date_parameter_handling(self):
        """Test handling of date parameters."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'):
            
            # Test with different date formats
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14"
            }
            
            pluang_plus = PluangPlusMember(config)
            
            # Verify date parameters are stored as provided
            assert pluang_plus.t_1 == "2025-01-15"
            assert pluang_plus.t_2 == "2025-01-14"
            assert isinstance(pluang_plus.t_1, str)
            assert isinstance(pluang_plus.t_2, str)

    def test_offset_parameter_scenarios(self):
        """Test different offset parameter scenarios."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'):
            
            # Test with offset = 0 (current day processing)
            config_offset_0 = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14"
            }
            
            pluang_plus_0 = PluangPlusMember(config_offset_0)
            assert pluang_plus_0.offset == 0
            
            # Test with offset = 1 (historical processing)
            config_offset_1 = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 1,
                "t_1": "2025-01-14",
                "t_2": "2025-01-13"
            }
            
            pluang_plus_1 = PluangPlusMember(config_offset_1)
            assert pluang_plus_1.offset == 1

    def test_utility_object_initialization(self):
        """Test that utility objects are properly initialized."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger') as mock_logger, \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils') as mock_spark_utils, \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils') as mock_io_utils, \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations') as mock_operations:
            
            # Setup mocks
            mock_logger.return_value = Mock()
            mock_spark_session = Mock()
            mock_spark_utils.return_value.create_spark_session.return_value = mock_spark_session
            mock_io_utils_instance = Mock()
            mock_io_utils.return_value = mock_io_utils_instance
            mock_operations_instance = Mock()
            mock_operations.return_value = mock_operations_instance
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14"
            }
            
            pluang_plus = PluangPlusMember(config)
            
            # Verify utility objects are assigned
            assert pluang_plus.spark == mock_spark_session
            assert pluang_plus.io_utils == mock_io_utils_instance
            assert pluang_plus.ops == mock_operations_instance
            
            # Verify initialization calls
            mock_spark_utils.assert_called_once_with("Pluang_Plus_Member_Validation")
            mock_io_utils.assert_called_once_with(mock_spark_session, config)
            mock_operations.assert_called_once_with(mock_spark_session)

    def test_logger_initialization(self):
        """Test logger initialization."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger') as mock_get_logger, \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'):
            
            mock_logger_instance = Mock()
            mock_get_logger.return_value = mock_logger_instance
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14"
            }
            
            pluang_plus = PluangPlusMember(config)
            
            # Verify logger is initialized and assigned
            mock_get_logger.assert_called_once()
            assert pluang_plus.logger == mock_logger_instance

    def test_config_access_patterns(self):
        """Test different configuration access patterns used in the class."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'):
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14",
                "pluang_plus_member_validity": {
                    "invested_value_threshold": 100000000,
                    "grace_period": 7
                }
            }
            
            pluang_plus = PluangPlusMember(config)
            
            # Test config.get() pattern (used for bucket_path)
            assert pluang_plus.config.get("bucket_path") == "s3a://test-bucket"
            assert pluang_plus.config.get("nonexistent_key") is None
            
            # Test config["key"] pattern (used for offset, t_1, t_2)
            assert pluang_plus.config["offset"] == 0
            assert pluang_plus.config["t_1"] == "2025-01-15"
            assert pluang_plus.config["t_2"] == "2025-01-14"
            
            # Test nested config access
            assert pluang_plus.config["pluang_plus_member_validity"]["invested_value_threshold"] == 100000000
            assert pluang_plus.config["pluang_plus_member_validity"]["grace_period"] == 7

    def test_kwargs_parameter_handling(self):
        """Test handling of additional keyword arguments."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'):
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14"
            }
            
            # Test with additional kwargs (should not cause errors)
            pluang_plus = PluangPlusMember(config, extra_param="test", debug=True)
            
            # Should initialize normally despite extra parameters
            assert pluang_plus.config == config
            assert pluang_plus.offset == 0
