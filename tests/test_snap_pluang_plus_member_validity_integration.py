import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch, call
from pyspark.sql import functions as F
from pyspark.sql.types import *

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestPluangPlusMemberIntegration:
    """Integration tests for PluangPlusMember functionality."""

    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.DateUtils')
    def test_generate_plus_member_file_integration(self, mock_date_utils, mock_operations, 
                                                  mock_io_utils, mock_spark_utils, mock_logger,
                                                  mock_pluang_plus_config, spark_session,
                                                  sample_invested_value_data, sample_prev_plus_member_data,
                                                  sample_accounts_data, sample_whitelisted_users_data,
                                                  sample_user_tag_mappings_data):
        """Test generate_plus_member_file method with realistic data flow."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15)
        
        # Mock IO operations to return sample data in sequence
        mock_io_utils.return_value.read_csv_file.side_effect = [
            sample_prev_plus_member_data,      # read_prev_plus_member_data
            sample_accounts_data,              # read_accounts_data  
            sample_whitelisted_users_data,     # read_whitelisted_plus_member_data
            sample_user_tag_mappings_data,     # read_user_tag_mappings_data (first call)
            sample_user_tag_mappings_data      # read_user_tag_mappings_data (second call in calculate_plus_member_status)
        ]
        
        pluang_plus = PluangPlusMember(mock_pluang_plus_config)
        pluang_plus.spark = spark_session
        pluang_plus.logger = Mock()
        
        # Test generate_plus_member_file
        result = pluang_plus.generate_plus_member_file(sample_invested_value_data)
        
        # Verify result structure
        assert result is not None
        assert "account_id" in result.columns
        assert "user_id" in result.columns
        assert "plus_status" in result.columns
        assert "grace_period" in result.columns
        assert "invested_value" in result.columns
        assert "processing_date" in result.columns
        assert "remove_today" in result.columns
        assert "add_today" in result.columns
        
        # Verify that write operations were called
        assert mock_io_utils.return_value.write_csv_file.call_count >= 2  # plus_intermediate and add_plus files
        
        # Check that we get some results
        count = result.count()
        assert count > 0

    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.DateUtils')
    def test_full_workflow_integration(self, mock_date_utils, mock_operations, 
                                      mock_io_utils, mock_spark_utils, mock_logger,
                                      mock_pluang_plus_config, spark_session,
                                      sample_cashin_data, sample_cashouts_data, 
                                      sample_funds_data, sample_portfolio_data):
        """Test the full workflow from start_processing to completion."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_spark_utils_instance.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15)
        
        # Create empty DataFrames for missing data sources
        empty_df = spark_session.createDataFrame([], StructType([
            StructField("account_id", LongType(), True),
            StructField("total_value", DoubleType(), True)
        ]))
        
        # Mock IO operations for fetch_assets_data
        mock_io_utils.return_value.read_csv_file.side_effect = [
            sample_cashin_data,     # cashin
            sample_cashouts_data,   # cashouts  
            sample_funds_data,      # funds
            empty_df,               # gold_gift_send
            empty_df,               # gold_gift_receive
            empty_df,               # gold_withdrawal
            empty_df,               # crypto_deposit
            empty_df,               # crypto_withdrawal
            empty_df,               # forex_topups
            empty_df,               # forex_cashouts
            sample_portfolio_data,  # portfolio data for get_realised_gain
            empty_df,               # previous day NIV for handle_current_day_processing
        ]
        
        pluang_plus = PluangPlusMember(mock_pluang_plus_config)
        
        # Test the full workflow
        pluang_plus.run()
        
        # Verify that start_processing was executed (indirectly through method calls)
        assert mock_io_utils.return_value.read_csv_file.call_count > 0
        assert mock_io_utils.return_value.write_csv_file.call_count > 0
        
        # Verify spark was stopped
        mock_spark_utils_instance.stop_spark.assert_called_once_with(spark_session)

    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations')
    @patch('src.jobs.snapshots.snap_pluang_plus_member_validity.DateUtils')
    def test_grace_period_logic_integration(self, mock_date_utils, mock_operations, 
                                           mock_io_utils, mock_spark_utils, mock_logger,
                                           spark_session):
        """Test grace period logic with realistic scenarios."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        # Setup mocks
        mock_logger.return_value = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = spark_session
        mock_date_utils.get_jkt_date.return_value = datetime(2025, 1, 15)
        
        config = {
            "bucket_path": "s3a://test-bucket",
            "pluang_plus_file_bucket": "test-plus-bucket",
            "offset": 0,
            "t_1": "2025-01-15",
            "t_2": "2025-01-14",
            "pluang_plus_member_validity": {
                "invested_value_threshold": *********,
                "grace_period": 7,
                "partner_id": 1000002,
                "plus_member_tag_id": 10001,
                "plus_intermediate_file": "plus_intermediate",
                "accounts_file": "accounts",
                "add_plus_file": "add_plus",
                "remove_plus_file": "remove_plus",
                "max_records_per_file": 1000,
                "whitelist_plus_member_file": "whitelist"
            },
            "user_tag_mappings_snapshot": {
                "snapshot_folder": "user_tag_mappings",
                "t_2_files_folder": "t_2_files"
            }
        }
        
        # Create test data for grace period scenarios
        prev_plus_member_data = [
            (1001, 101, True, 3, datetime(2025, 1, 10), datetime(2025, 1, 14), ********.0),  # Grace period 3, below threshold
            (1002, 102, True, 7, datetime(2025, 1, 10), datetime(2025, 1, 14), ********.0),  # Grace period 7 (at limit)
            (1003, 103, True, 8, datetime(2025, 1, 10), datetime(2025, 1, 14), ********.0),  # Grace period 8 (over limit)
        ]
        
        current_invested_data = [
            (1001, ********.0),  # Still below threshold
            (1002, ********.0),  # Still below threshold  
            (1003, ********.0),  # Still below threshold
        ]
        
        accounts_data = [
            (1001, 101, 1000002),
            (1002, 102, 1000002),
            (1003, 103, 1000002)
        ]
        
        # Create DataFrames
        prev_plus_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("plus_status", BooleanType(), True),
            StructField("grace_period", IntegerType(), True),
            StructField("created", TimestampType(), True),
            StructField("updated", TimestampType(), True),
            StructField("invested_value", DoubleType(), True)
        ])
        
        current_invested_schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("invested_value", DoubleType(), True)
        ])
        
        accounts_schema = StructType([
            StructField("id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("partner_id", LongType(), True)
        ])
        
        df_prev_plus = spark_session.createDataFrame(prev_plus_member_data, prev_plus_schema)
        df_current_invested = spark_session.createDataFrame(current_invested_data, current_invested_schema)
        df_accounts = spark_session.createDataFrame(accounts_data, accounts_schema)
        
        # Empty DataFrames for other required data
        empty_whitelisted = spark_session.createDataFrame([], StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("is_whitelisted", BooleanType(), True)
        ]))
        
        empty_user_tags = spark_session.createDataFrame([], StructType([
            StructField("user_id", LongType(), True),
            StructField("tag_id", LongType(), True),
            StructField("tag_pool_id", LongType(), True),
            StructField("tag_name", StringType(), True)
        ]))
        
        # Mock IO operations
        mock_io_utils.return_value.read_csv_file.side_effect = [
            df_prev_plus,       # read_prev_plus_member_data
            df_accounts,        # read_accounts_data
            empty_whitelisted,  # read_whitelisted_plus_member_data
            empty_user_tags,    # read_user_tag_mappings_data (first call)
            empty_user_tags     # read_user_tag_mappings_data (second call)
        ]
        
        pluang_plus = PluangPlusMember(config)
        pluang_plus.spark = spark_session
        pluang_plus.logger = Mock()
        
        # Test generate_plus_member_file with grace period logic
        result = pluang_plus.generate_plus_member_file(df_current_invested)
        
        # Verify result structure and logic
        assert result is not None
        result_list = result.collect()
        
        # Check grace period increments and status changes
        for row in result_list:
            if row["user_id"] == 101:  # Grace period should increment from 3 to 4
                assert row["grace_period"] == 4
                assert row["plus_status"] == True  # Still within grace period
            elif row["user_id"] == 102:  # Grace period should increment from 7 to 8
                assert row["grace_period"] == 8
                assert row["plus_status"] == False  # Exceeded grace period
            elif row["user_id"] == 103:  # Grace period should increment from 8 to 9
                assert row["grace_period"] == 9
                assert row["plus_status"] == False  # Already exceeded grace period

    def test_data_validation_integration(self, spark_session):
        """Test data validation and error handling in integration scenarios."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils') as mock_io_utils, \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.DateUtils'):
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14",
                "pluang_plus_member_validity": {
                    "invested_value_threshold": *********,
                    "grace_period": 7
                }
            }
            
            pluang_plus = PluangPlusMember(config)
            pluang_plus.spark = spark_session
            pluang_plus.logger = Mock()
            
            # Test with invalid/missing data
            invalid_data = [
                (None, 101, 1********.0),  # Null account_id
                (1002, None, ********.0),  # Null user_id
                (1003, 103, None),         # Null invested_value
            ]
            
            schema = StructType([
                StructField("account_id", LongType(), True),
                StructField("user_id", LongType(), True),
                StructField("invested_value", DoubleType(), True)
            ])
            
            invalid_df = spark_session.createDataFrame(invalid_data, schema)
            
            # The system should handle null values gracefully
            # Test that filtering works correctly with null values
            filtered_df = invalid_df.filter(
                F.col("user_id").isNotNull() & F.col("invested_value").isNotNull()
            )
            
            result_count = filtered_df.count()
            # Should filter out rows with null user_id or invested_value
            assert result_count == 0  # All rows have at least one null value in required fields

    def test_edge_case_empty_datasets_integration(self, spark_session):
        """Test handling of completely empty datasets in integration flow."""
        from src.jobs.snapshots.snap_pluang_plus_member_validity import PluangPlusMember
        
        with patch('src.jobs.snapshots.snap_pluang_plus_member_validity.get_logger'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.SparkUtils'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.IOUtils') as mock_io_utils, \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.Operations'), \
             patch('src.jobs.snapshots.snap_pluang_plus_member_validity.DateUtils'):
            
            config = {
                "bucket_path": "s3a://test-bucket",
                "pluang_plus_file_bucket": "test-plus-bucket",
                "offset": 0,
                "t_1": "2025-01-15",
                "t_2": "2025-01-14",
                "pluang_plus_member_validity": {
                    "invested_value_threshold": *********,
                    "grace_period": 7,
                    "portfolio": "portfolio/snapshots"
                }
            }
            
            # Create empty DataFrames with correct schemas
            empty_portfolio_schema = StructType([
                StructField("account_id", LongType(), True),
                StructField("realised_gain", DoubleType(), True)
            ])
            
            empty_assets_schema = StructType([
                StructField("account_id", LongType(), True),
                StructField("total_topup", DoubleType(), True),
                StructField("total_cashout", DoubleType(), True)
            ])
            
            empty_portfolio_df = spark_session.createDataFrame([], empty_portfolio_schema)
            empty_assets_df = spark_session.createDataFrame([], empty_assets_schema)
            
            # Mock all IO operations to return empty DataFrames
            mock_io_utils.return_value.read_csv_file.return_value = empty_portfolio_df
            
            pluang_plus = PluangPlusMember(config)
            pluang_plus.spark = spark_session
            pluang_plus.logger = Mock()
            
            # Test get_realised_gain with empty data
            result = pluang_plus.get_realised_gain()
            
            # Should handle empty data gracefully
            assert result is not None
            assert result.count() == 0
            assert "account_id" in result.columns
            assert "realised_gain_value" in result.columns
